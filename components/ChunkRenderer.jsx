import React, { memo, useRef, useState, useEffect, useCallback } from 'react';
import { Row, Col, Input, Space, Button, Popconfirm, Divider } from 'antd';
import {
  EditOutlined, SaveOutlined, CloseOutlined, ArrowRightOutlined,
  PauseOutlined, DeleteOutlined, ClearOutlined
} from '@ant-design/icons';
import { isEqual } from 'lodash';
import { useStore } from '../stores/index.js';
import ThinkDisplay from './ThinkDisplay';
import EnhancedTextArea from './EnhancedTextArea';

// 合并编辑显示组件
const MergedEditDisplay = ({ type, mergedText, setMergedText, onSave, onCancel, toolbarHeight = 80 }) => {
  // 当外部 mergedText 变化时，更新本地状态
  useEffect(() => {
    // EnhancedTextArea 现在内置防抖，不需要额外处理
  }, [mergedText]);

  // 处理输入变化 - 直接传递给 setMergedText，防抖由 EnhancedTextArea 处理
  const handleTextChange = (e) => {
    const value = e.target.value;
    console.log(`[MergedEditDisplay] handleTextChange called. This will call setMergedText in ArticleEditPage. Value: "${value}"`);
    setMergedText(value);
  };

  // 动态计算需要减去的高度
  const calculateTextareaHeight = () => {
    const baseHeight = toolbarHeight; // 工具栏高度
    const titleHeight = 60; // 原文/译文标题行高度
    const buttonHeight = 50; // 保存/取消按钮区域高度
    const citationHeight = 120; // 来源区域预估高度
    const refsHeight = 120; // 参考区域预估高度
    const padding = 40; // 页面内边距和间距

    const totalOffset = baseHeight + titleHeight + buttonHeight + citationHeight + refsHeight + padding;
    return `calc(100vh - ${totalOffset}px)`;
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', width: '100%', height: '100vh' }}>
      <EnhancedTextArea
        value={mergedText}
        onChange={handleTextChange}
        showLineNumbers={true}
        style={{
          width: '100%',
          fontSize: '14px',
          lineHeight: '1.6',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          marginRight: '8px', // 给滚动条留出空间
          height: calculateTextareaHeight(), // 动态计算高度
          resize: 'vertical'
        }}
        placeholder={`编辑${type === 'original' ? '原文' : '译文'}内容，支持 HTML 标签...`}
      />
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
        <Space>
          <Button
            icon={<SaveOutlined />}
            onClick={onSave}
            size="small"
          >
            保存
          </Button>
          <Button
            icon={<CloseOutlined />}
            onClick={onCancel}
            size="small"
          >
            取消
          </Button>
        </Space>
      </div>
    </div>
  );
};

// 渲染计数器，用于跟踪每个区块的渲染次数
const renderCounts = {};

const ChunkRenderer = memo((props) => {
  // 增加渲染计数
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  const renderCount = renderCountRef.current;

  console.log(`[ChunkRenderer] 组件渲染 #${renderCount}, chunkIndex=${props.chunkIndex}, isActivelyTranslating=${props.isActivelyTranslating}, hasTranslatedItems=${props.translatedItemsForChunk?.length > 0}`);

  // 获取 think 相关状态
  const chunkThinkContents = useStore(state => state.chunkThinkContents);
  const chunkThinkStates = useStore(state => state.chunkThinkStates);

  // 获取当前区块的 think 状态
  const currentThinkContent = chunkThinkContents.get(props.chunkIndex) || '';
  const currentThinkState = chunkThinkStates.get(props.chunkIndex) || { isThinking: false, isCompleted: false };


  const {
    items,
    translatedItemsForChunk,
    chunkIndex,
    editingState,
    editText,
    debouncedSetEditText,
    handleEditStart,
    handleEditSave,
    handleEditCancel,
    isActivelyTranslating,
    disableTranslateButton,
    stoppingChunks,
    handleTranslateChunk,
    handleStopChunkTranslation,
    handleClearTranslatedChunk,
    handleDeleteChunk,
    showTranslationOnly,
    renderOriginalContentInternal, // This prop might need to be removed if logic is fully self-contained or passed differently
    renderTranslatedContentInternal, // This prop is now handled by TranslatedChunkDisplay and specific logic here
    translationContainerRefs,
    updateTranslationDOM,
    // 新增的合并编辑状态
    isMergedOriginalEditing = false,
    isMergedTranslatedEditing = false,
    // 动态高度信息
    toolbarHeight = 80,
    // checkTagCountMismatch, // This logic might be internal to renderTranslatedContentForChunkRenderer or passed to it
    // formatConversionNeeded, // REMOVED
    // handleAttemptAIFormatConversion, // REMOVED
    // clearFormatConversionNeeded, // REMOVED
  } = props; // Destructure all props here

  // Original component logic starts here, using destructured props
  const isEditingOriginal = editingState.type === 'original' && editingState.index === chunkIndex;
  const isEditingTranslated = editingState.type === 'translated' && editingState.index === chunkIndex;

  const renderOriginalContentDisplay = () => {
    if (isEditingOriginal) {
      return (
        <EnhancedTextArea
          defaultValue={editText}
          onChange={debouncedSetEditText}
          showLineNumbers={true}
          autoSize={{ minRows: 6, maxRows: 25 }}
          style={{ width: '100%', resize: 'vertical' }}
        />
      );
    } else {
      return (
        <div className="original-chunk-content" style={{ minHeight: '200px' }}>
          {items.map(item => {
            const { tag, id, ...props } = item;
            const Tag = `${tag}`;
            if (tag === 'img') {
              return <Tag key={id} {...props} loading="lazy" />;
            }
            if (tag === 'table' && props.children) {
              const tableHtml = props.children;
              delete props.children;
              return <Tag key={id} {...props} dangerouslySetInnerHTML={{ __html: tableHtml }} />;
            }
            if (props.children) {
              const htmlContent = props.children;
              delete props.children;
              return <Tag key={id} {...props} dangerouslySetInnerHTML={{ __html: htmlContent }} />;
            }
            return <Tag key={id} {...props} />;
          })}
        </div>
      );
    }
  };

  const renderEditButtonsDisplay = (type) => {
    const isEditingThis = editingState.type === type && editingState.index === chunkIndex;

    if (isEditingThis) {
      return (
        <Space style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
          <Button icon={<SaveOutlined />} size="small" onClick={handleEditSave}>保存</Button>
          <Button icon={<CloseOutlined />} size="small" onClick={handleEditCancel}>取消</Button>
        </Space>
      );
    }

    // For non-editing mode, always show the edit button regardless of content
    return (
      <Button
        type="text"
        icon={<EditOutlined />}
        size="small"
        onClick={() => handleEditStart(type, chunkIndex)}
        title={type === 'original' ? '编辑原文' : '编辑译文'}
      />
    );
  };



  if (showTranslationOnly) {
    return (
      <Row key={`chunk-row-${chunkIndex}-translation-only`} className="chunk-row" gutter={16}>
        <Col span={24} style={{ display: 'flex' }}>
          <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            {/* Think 过程显示 */}
            {(currentThinkContent || currentThinkState.isThinking) && (
              <ThinkDisplay
                thinkContent={currentThinkContent}
                isThinking={currentThinkState.isThinking}
                isCompleted={currentThinkState.isCompleted}
                style={{ marginBottom: '12px' }}
              />
            )}

            <div
              className={`translated-content-container ${isEditingTranslated ? 'editing-mode' : ''}`}
              style={{ flexGrow: 1, minHeight: isEditingTranslated ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
            >
              {/* renderTranslatedContentInternal is called here */}
              {renderTranslatedContentInternal(items, translatedItemsForChunk, chunkIndex)}
            </div>
            <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
              {isEditingTranslated
                ? renderEditButtonsDisplay('translated')
                : (
                  <Space align="baseline" size={0}>
                    <Popconfirm
                      title="确认清空此区块译文?"
                      description="此操作将移除当前译文内容，但原文保留。"
                      onConfirm={() => handleClearTranslatedChunk(chunkIndex)}
                      okText="确认清空"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                      disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                    >
                      <Button
                        type="text"
                        danger
                        icon={<ClearOutlined />}
                        size="small"
                        title="清空译文"
                        disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                      />
                    </Popconfirm>
                    {renderEditButtonsDisplay('translated')}
                  </Space>
                )
              }
            </div>
          </div>
        </Col>
        <Col span={24}><Divider style={{ marginTop: '10px', marginBottom: '10px'}} /></Col>
      </Row>
    );
  } else {
    // 合并编辑模式下，只渲染第一个chunk，其他chunk不渲染
    if ((isMergedOriginalEditing || isMergedTranslatedEditing) && chunkIndex !== 0) {
      return null;
    }

    // 根据合并编辑状态决定列的显示
    const showOriginal = !isMergedOriginalEditing;
    const showTranslated = !isMergedTranslatedEditing;
    // 保持原有的12/12布局，即使在合并编辑模式下也不改变宽度
    const originalSpan = 12;
    const translatedSpan = 12;

    return (
      <Row key={`chunk-row-${chunkIndex}-dual`} className="chunk-row" gutter={16}>
        {/* 原文列 */}
        <Col span={originalSpan} style={{ display: 'flex' }}>
          {isMergedOriginalEditing ? (
            /* 原文合并编辑模式 - 只在第一个chunk显示textarea */
            <MergedEditDisplay
              type="original"
              mergedText={props.mergedOriginalText}
              setMergedText={props.setMergedOriginalText}
              onSave={props.handleSaveMergedOriginalEdit}
              onCancel={props.handleCancelMergedOriginalEdit}
              toolbarHeight={toolbarHeight}
            />
          ) : (
            /* 正常原文显示模式 */
            <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              <div
                className={`original-content-container ${isEditingOriginal ? 'editing-mode' : ''}`}
                style={{ flexGrow: 1, minHeight: isEditingOriginal ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
              >
                {renderOriginalContentDisplay()}
              </div>
              <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                {isEditingOriginal
                  ? renderEditButtonsDisplay('original')
                  : (
                    <Space align="baseline" size={0}>
                      <Popconfirm
                        title="确认删除此区块?"
                        description="将同时删除原文和译文，此操作无法撤销。"
                        onConfirm={() => handleDeleteChunk(chunkIndex)}
                        okText="确认删除"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                      >
                        <Button type="text" danger icon={<DeleteOutlined />} size="small" title="删除此块" />
                      </Popconfirm>
                      {renderEditButtonsDisplay('original')}
                      {isActivelyTranslating ? (
                        <Button
                          type="text"
                          danger
                          size="small"
                          icon={<PauseOutlined />}
                          onClick={() => handleStopChunkTranslation(chunkIndex)}
                          title="停止翻译"
                          loading={stoppingChunks.has(chunkIndex)}
                          disabled={stoppingChunks.has(chunkIndex)}
                        />
                      ) : (
                        <Button
                          type="text"
                          size="small"
                          icon={<ArrowRightOutlined />}
                          onClick={() => handleTranslateChunk(chunkIndex)}
                          title="翻译此块"
                          disabled={disableTranslateButton}
                        />
                      )}
                    </Space>
                  )
                }
              </div>
            </div>
          )}
        </Col>

        {/* 译文列 */}
        <Col span={translatedSpan} style={{ display: 'flex' }}>
          {isMergedTranslatedEditing ? (
            /* 译文合并编辑模式 - 只在第一个chunk显示textarea */
            <MergedEditDisplay
              type="translated"
              mergedText={props.mergedTranslatedText}
              setMergedText={props.setMergedTranslatedText}
              onSave={props.handleSaveMergedTranslatedEdit}
              onCancel={props.handleCancelMergedTranslatedEdit}
              toolbarHeight={toolbarHeight}
            />
          ) : (
            /* 正常译文显示模式 */
            <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              {/* Think 过程显示 */}
              {(currentThinkContent || currentThinkState.isThinking) && (
                <ThinkDisplay
                  thinkContent={currentThinkContent}
                  isThinking={currentThinkState.isThinking}
                  isCompleted={currentThinkState.isCompleted}
                  style={{ marginBottom: '12px' }}
                />
              )}

              <div
                className={`translated-content-container ${isEditingTranslated ? 'editing-mode' : ''}`}
                style={{ flexGrow: 1, minHeight: isEditingTranslated ? '300px' : '200px', display: 'flex', flexDirection: 'column' }}
              >
                {/* renderTranslatedContentInternal is called here */}
                {renderTranslatedContentInternal(items, translatedItemsForChunk, chunkIndex)}
              </div>
              <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                {isEditingTranslated
                  ? renderEditButtonsDisplay('translated')
                  : (
                    <Space align="baseline" size={0}>
                      <Popconfirm
                        title="确认清空此区块译文?"
                        description="此操作将移除当前译文内容，但原文保留。"
                        onConfirm={() => handleClearTranslatedChunk(chunkIndex)}
                        okText="确认清空"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                        disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<ClearOutlined />}
                          size="small"
                          title="清空译文"
                          disabled={!translatedItemsForChunk || translatedItemsForChunk.length === 0}
                        />
                      </Popconfirm>
                      {renderEditButtonsDisplay('translated')}
                    </Space>
                  )
                }
              </div>
            </div>
          )}
        </Col>
        <Col span={24}><Divider style={{ marginTop: '10px', marginBottom: '10px'}} /></Col>
      </Row>
    );
  }
}, (prevProps, nextProps) => {
  return (
    prevProps.chunkIndex === nextProps.chunkIndex &&
    isEqual(prevProps.items, nextProps.items) &&
    isEqual(prevProps.translatedItemsForChunk, nextProps.translatedItemsForChunk) &&
    prevProps.editingState.type === nextProps.editingState.type &&
    prevProps.editingState.index === nextProps.editingState.index &&
    (prevProps.editingState.index !== prevProps.chunkIndex || prevProps.editText === nextProps.editText) &&
    prevProps.isActivelyTranslating === nextProps.isActivelyTranslating &&
    prevProps.disableTranslateButton === nextProps.disableTranslateButton &&
    isEqual(prevProps.stoppingChunks, nextProps.stoppingChunks) &&
    prevProps.showTranslationOnly === nextProps.showTranslationOnly &&
    prevProps.isMergedOriginalEditing === nextProps.isMergedOriginalEditing &&
    prevProps.isMergedTranslatedEditing === nextProps.isMergedTranslatedEditing &&
    // 当处于合并编辑模式时，必须比较文本内容是否发生变化
    (!nextProps.isMergedOriginalEditing || prevProps.mergedOriginalText === nextProps.mergedOriginalText) &&
    (!nextProps.isMergedTranslatedEditing || prevProps.mergedTranslatedText === nextProps.mergedTranslatedText) &&
    // prevProps.onEditStart === nextProps.onEditStart && // handleEditStart is stable
    // prevProps.handleEditSave === nextProps.handleEditSave && // handleEditSave is stable
    // prevProps.handleEditCancel === nextProps.handleEditCancel && // handleEditCancel is stable
    // prevProps.handleTranslateChunk === nextProps.handleTranslateChunk && // stable
    // prevProps.handleStopChunkTranslation === nextProps.handleStopChunkTranslation && // stable
    // prevProps.handleClearTranslatedChunk === nextProps.handleClearTranslatedChunk && // stable
    // prevProps.handleDeleteChunk === nextProps.handleDeleteChunk && // stable
    prevProps.renderTranslatedContentInternal === nextProps.renderTranslatedContentInternal
    // isEqual(prevProps.formatConversionNeeded, nextProps.formatConversionNeeded) && // REMOVED
    // prevProps.handleAttemptAIFormatConversion === nextProps.handleAttemptAIFormatConversion && // REMOVED
    // prevProps.clearFormatConversionNeeded === nextProps.clearFormatConversionNeeded // REMOVED
  );
});

export default ChunkRenderer;