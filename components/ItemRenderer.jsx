import React, { useState, memo } from 'react';
import { Button, Space, Input, Popconfirm } from 'antd';
import {
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteOutlined,
  ClearOutlined
} from '@ant-design/icons';
import EnhancedTextArea from './EnhancedTextArea';

// 添加CSS样式
const itemStyles = `
  .item-container {
    padding: 8px;
    margin: 4px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    background-color: transparent;
    transition: all 0.15s ease;
    position: relative;
  }

  .item-container:hover {
    background-color: #f0f8ff !important;
    border-color: #1890ff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  .item-hovered {
    background-color: #f0f8ff !important;
    border-color: #1890ff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }

  .item-container .action-buttons {
    opacity: 0;
    transition: opacity 0.15s ease;
    transform: translateX(10px);
  }

  .item-container:hover .action-buttons,
  .item-hovered .action-buttons {
    opacity: 1;
    transform: translateX(0);
  }

  /* 对应item高亮效果 - 使用CSS变量来实现跨组件的hover效果 */
  .chunk-container {
    position: relative;
  }

  .chunk-container:hover .item-container[data-type="original"]:hover ~ .translated-content .item-container[data-type="translated"][data-item-index] {
    background-color: #f9f9f9 !important;
    border-color: #d9d9d9 !important;
  }

  .chunk-container:hover .item-container[data-type="translated"]:hover ~ .original-content .item-container[data-type="original"][data-item-index] {
    background-color: #f9f9f9 !important;
    border-color: #d9d9d9 !important;
  }

  /* 编辑状态样式 */
  .item-container.editing {
    background-color: #fff7e6 !important;
    border-color: #ffa940 !important;
    box-shadow: 0 2px 8px rgba(255, 169, 64, 0.2);
  }

  .item-container.editing .action-buttons {
    opacity: 1;
    transform: translateX(0);
  }
`;

// 注入样式
if (typeof document !== 'undefined' && !document.getElementById('item-renderer-styles')) {
  const style = document.createElement('style');
  style.id = 'item-renderer-styles';
  style.textContent = itemStyles;
  document.head.appendChild(style);
}

const ItemRenderer = memo(({
  item,
  itemIndex,
  chunkIndex,
  type, // 'original' | 'translated'
  isEditing,
  editText,
  onEditStart,
  onEditSave,
  onEditCancel,
  onEditTextChange,
  onInsert,
  onDelete,
  onClear,
  isHovered
}) => {

  const renderItemContent = () => {
    if (item === null) {
      return <p style={{ color: '#999', fontStyle: 'italic' }}>{'{null}'}</p>;
    }

    if (!item) {
      return <p style={{ color: '#999', fontStyle: 'italic' }}>{'{空行}'}</p>;
    }

    const { tag, id, ...props } = item;
    const Tag = tag;

    if (tag === 'img') {
      return <Tag {...props} loading="lazy" />;
    }

    if (tag === 'table' && props.children) {
      const tableHtml = props.children;
      const { children, ...otherProps } = props;
      return <Tag {...otherProps} dangerouslySetInnerHTML={{ __html: tableHtml }} />;
    }

    if (props.children) {
      const htmlContent = props.children;
      const { children, ...otherProps } = props;
      return <Tag {...otherProps} dangerouslySetInnerHTML={{ __html: htmlContent }} />;
    }

    // 处理空内容的情况
    if (tag === 'p' && (!props.children || props.children === '')) {
      return <p style={{ color: '#999', fontStyle: 'italic' }}>{'{空行}'}</p>;
    }

    return <Tag {...props} />;
  };

  const renderActionButtons = () => {
    if (isEditing) {
      return (
        <Space size="small">
          <Button 
            icon={<SaveOutlined />} 
            size="small" 
            type="primary"
            onClick={onEditSave}
          >
            保存
          </Button>
          <Button 
            icon={<CloseOutlined />} 
            size="small" 
            onClick={onEditCancel}
          >
            取消
          </Button>
        </Space>
      );
    }

    return (
      <Space size="small" className="action-buttons">
        {/* 插入按钮 */}
        <Button
          icon={<PlusOutlined />}
          size="small"
          type="text"
          title="在下方插入新项目"
          onClick={() => onInsert?.(type, chunkIndex, itemIndex + 1)}
        />
        
        {/* 编辑按钮 */}
        <Button
          icon={<EditOutlined />}
          size="small"
          type="text"
          title={`编辑${type === 'original' ? '原文' : '译文'}`}
          onClick={() => onEditStart?.(type, chunkIndex, itemIndex, item)}
        />
        
        {/* 清空按钮 */}
        <Popconfirm
          title={`确认清空此${type === 'original' ? '原文' : '译文'}项目?`}
          description="此操作将清空当前项目内容。"
          onConfirm={() => onClear?.(type, chunkIndex, itemIndex)}
          okText="确认清空"
          cancelText="取消"
          okButtonProps={{ danger: true }}
        >
          <Button
            icon={<ClearOutlined />}
            size="small"
            type="text"
            danger
            title={`清空${type === 'original' ? '原文' : '译文'}`}
          />
        </Popconfirm>
        
        {/* 删除按钮（只对原文显示） */}
        {type === 'original' && (
          <Popconfirm
            title="确认删除此项目?"
            description="将同时删除原文和对应的译文，此操作无法撤销。"
            onConfirm={() => onDelete?.(chunkIndex, itemIndex)}
            okText="确认删除"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Button
              icon={<DeleteOutlined />}
              size="small"
              type="text"
              danger
              title="删除此项目"
            />
          </Popconfirm>
        )}
      </Space>
    );
  };

  return (
    <div
      className={`item-container ${isEditing ? 'editing' : ''}`}
      data-item-index={itemIndex}
      data-chunk-index={chunkIndex}
      data-type={type}
    >
      {isEditing ? (
        <div style={{ marginBottom: '8px' }}>
          <EnhancedTextArea
            value={editText}
            onChange={(e) => onEditTextChange?.(e.target.value)}
            placeholder={`编辑${type === 'original' ? '原文' : '译文'}内容...`}
            showLineNumbers={true}
            autoSize={{ minRows: 3, maxRows: 10 }}
            style={{ marginBottom: '8px' }}
          />
        </div>
      ) : (
        <div style={{ marginBottom: '8px' }}>
          {renderItemContent()}
        </div>
      )}
      
      <div style={{ 
        display: 'flex', 
        justifyContent: 'flex-end',
        alignItems: 'center'
      }}>
        {renderActionButtons()}
      </div>
    </div>
  );
});

ItemRenderer.displayName = 'ItemRenderer';

export default ItemRenderer;
